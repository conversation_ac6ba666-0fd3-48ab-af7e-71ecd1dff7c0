import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'views/calendar_view.dart';
import 'controllers/activity_controller.dart';
import 'controllers/calendar_controller.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ActivityController()..init()),
        ChangeNotifierProxyProvider<ActivityController, CalendarController>(
          create: (context) => CalendarController(context.read<ActivityController>()),
          update: (context, activityController, previous) =>
              previous ?? CalendarController(activityController),
        ),
      ],
      child: MaterialApp(
        title: 'Calendario de Actividades',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
          useMaterial3: true,
        ),
        home: const CalendarView(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}


