import 'package:flutter/material.dart';
import '../models/activity.dart';
import '../models/activity_completion.dart';
import '../models/recurrence_pattern.dart';
import '../services/memory_storage_service.dart';

class ActivityController extends ChangeNotifier {
  final MemoryStorageService _storageService = MemoryStorageService();
  
  List<Activity> _activities = [];
  List<ActivityCompletion> _completions = [];
  bool _isLoading = false;

  // Getters
  List<Activity> get activities => List.unmodifiable(_activities);
  List<ActivityCompletion> get completions => List.unmodifiable(_completions);
  bool get isLoading => _isLoading;

  // Inicializar el controlador
  Future<void> init() async {
    await _loadData();
    // Agregar datos de ejemplo si no hay actividades
    if (_activities.isEmpty) {
      await _addSampleData();
    }
  }

  // Cargar datos desde el almacenamiento
  Future<void> _loadData() async {
    _setLoading(true);
    try {
      _activities = await _storageService.loadActivities();
      _completions = await _storageService.loadCompletions();
    } catch (e) {
      print('Error loading data: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Establecer estado de carga
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Crear nueva actividad
  Future<void> createActivity(Activity activity) async {
    try {
      _activities.add(activity);
      await _storageService.saveActivity(activity);
      notifyListeners();
    } catch (e) {
      print('Error creating activity: $e');
      // Revertir cambio en caso de error
      _activities.removeWhere((a) => a.id == activity.id);
      rethrow;
    }
  }

  // Actualizar actividad existente
  Future<void> updateActivity(Activity updatedActivity) async {
    try {
      final index = _activities.indexWhere((a) => a.id == updatedActivity.id);
      if (index >= 0) {
        _activities[index] = updatedActivity;
        await _storageService.saveActivity(updatedActivity);
        notifyListeners();
      }
    } catch (e) {
      print('Error updating activity: $e');
      await _loadData(); // Recargar datos en caso de error
      rethrow;
    }
  }

  // Eliminar actividad
  Future<void> deleteActivity(String activityId) async {
    try {
      _activities.removeWhere((a) => a.id == activityId);
      _completions.removeWhere((c) => c.activityId == activityId);

      await _storageService.deleteActivity(activityId);
      notifyListeners();
    } catch (e) {
      print('Error deleting activity: $e');
      await _loadData(); // Recargar datos en caso de error
      rethrow;
    }
  }

  // Obtener actividades para una fecha específica
  List<Activity> getActivitiesForDate(DateTime date) {
    return _activities.where((activity) => activity.shouldOccurOn(date)).toList();
  }

  // Marcar actividad como completada/no completada
  Future<void> toggleActivityCompletion(String activityId, DateTime date, {String? notes}) async {
    try {
      final existingCompletion = _completions.firstWhere(
        (c) => c.activityId == activityId && c.date == DateTime(date.year, date.month, date.day),
        orElse: () => ActivityCompletion.create(
          activityId: activityId,
          date: date,
          isCompleted: false,
        ),
      );

      final newCompletion = existingCompletion.copyWith(
        isCompleted: !existingCompletion.isCompleted,
        notes: notes,
      );

      // Actualizar en la lista local
      final index = _completions.indexWhere((c) => 
          c.activityId == activityId && c.date == DateTime(date.year, date.month, date.day));
      
      if (index >= 0) {
        _completions[index] = newCompletion;
      } else {
        _completions.add(newCompletion);
      }

      await _storageService.saveCompletion(newCompletion);
      notifyListeners();
    } catch (e) {
      print('Error toggling activity completion: $e');
      await _loadData(); // Recargar datos en caso de error
      rethrow;
    }
  }

  // Verificar si una actividad está completada en una fecha específica
  bool isActivityCompletedOnDate(String activityId, DateTime date) {
    final targetDate = DateTime(date.year, date.month, date.day);
    final completion = _completions.firstWhere(
      (c) => c.activityId == activityId && c.date == targetDate,
      orElse: () => ActivityCompletion.create(
        activityId: activityId,
        date: date,
        isCompleted: false,
      ),
    );
    return completion.isCompleted;
  }

  // Obtener estadísticas de cumplimiento para una actividad
  Map<String, dynamic> getActivityStats(String activityId, {DateTime? startDate, DateTime? endDate}) {
    final activity = _activities.firstWhere((a) => a.id == activityId);
    final start = startDate ?? activity.startDate;
    final end = endDate ?? DateTime.now();
    
    int totalExpected = 0;
    int totalCompleted = 0;
    
    for (DateTime date = start; date.isBefore(end) || date.isAtSameMomentAs(end); date = date.add(const Duration(days: 1))) {
      if (activity.shouldOccurOn(date)) {
        totalExpected++;
        if (isActivityCompletedOnDate(activityId, date)) {
          totalCompleted++;
        }
      }
    }
    
    return {
      'totalExpected': totalExpected,
      'totalCompleted': totalCompleted,
      'completionRate': totalExpected > 0 ? (totalCompleted / totalExpected) : 0.0,
      'streak': _calculateCurrentStreak(activityId),
    };
  }

  // Calcular racha actual de cumplimiento
  int _calculateCurrentStreak(String activityId) {
    final activity = _activities.firstWhere((a) => a.id == activityId);
    int streak = 0;
    DateTime currentDate = DateTime.now();
    
    while (currentDate.isAfter(activity.startDate) || currentDate.isAtSameMomentAs(activity.startDate)) {
      if (activity.shouldOccurOn(currentDate)) {
        if (isActivityCompletedOnDate(activityId, currentDate)) {
          streak++;
        } else {
          break;
        }
      }
      currentDate = currentDate.subtract(const Duration(days: 1));
    }
    
    return streak;
  }

  // Obtener actividad por ID
  Activity? getActivityById(String id) {
    try {
      return _activities.firstWhere((a) => a.id == id);
    } catch (e) {
      return null;
    }
  }

  // Recargar datos
  Future<void> refresh() async {
    await _loadData();
  }

  // Agregar datos de ejemplo
  Future<void> _addSampleData() async {
    final sampleActivities = [
      Activity.create(
        name: 'Ejercicio matutino',
        description: 'Rutina de ejercicios de 30 minutos',
        startDate: DateTime.now(),
        scheduledTime: const TimeOfDay(hour: 8, minute: 0),
        recurrencePattern: RecurrencePattern.daily(),
        category: 'Ejercicio',
      ),
      Activity.create(
        name: 'Leer 30 minutos',
        description: 'Lectura diaria para desarrollo personal',
        startDate: DateTime.now(),
        scheduledTime: const TimeOfDay(hour: 20, minute: 0),
        recurrencePattern: RecurrencePattern.daily(),
        category: 'Personal',
      ),
      Activity.create(
        name: 'Reunión de equipo',
        description: 'Reunión semanal del equipo de trabajo',
        startDate: DateTime.now(),
        scheduledTime: const TimeOfDay(hour: 10, minute: 0),
        recurrencePattern: RecurrencePattern.weekly(weekdays: [1, 3, 5]), // Lun, Mié, Vie
        category: 'Trabajo',
      ),
    ];

    for (final activity in sampleActivities) {
      await createActivity(activity);
    }
  }
}
