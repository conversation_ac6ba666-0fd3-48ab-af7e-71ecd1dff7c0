import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/activity.dart';
import '../models/activity_completion.dart';

class StorageService {
  static const String _activitiesKey = 'activities';
  static const String _completionsKey = 'activity_completions';

  // Singleton pattern
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  SharedPreferences? _prefs;

  // Inicializar SharedPreferences
  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // Guardar actividades
  Future<void> saveActivities(List<Activity> activities) async {
    await init();
    final activitiesJson = activities.map((activity) => activity.toJson()).toList();
    final activitiesString = jsonEncode(activitiesJson);
    await _prefs!.setString(_activitiesKey, activitiesString);
  }

  // Cargar actividades
  Future<List<Activity>> loadActivities() async {
    await init();
    final activitiesString = _prefs!.getString(_activitiesKey);
    if (activitiesString == null) return [];

    try {
      final activitiesJson = jsonDecode(activitiesString) as List;
      return activitiesJson
          .map((json) => Activity.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error loading activities: $e');
      return [];
    }
  }

  // Guardar completions
  Future<void> saveCompletions(List<ActivityCompletion> completions) async {
    await init();
    final completionsJson = completions.map((completion) => completion.toJson()).toList();
    final completionsString = jsonEncode(completionsJson);
    await _prefs!.setString(_completionsKey, completionsString);
  }

  // Cargar completions
  Future<List<ActivityCompletion>> loadCompletions() async {
    await init();
    final completionsString = _prefs!.getString(_completionsKey);
    if (completionsString == null) return [];

    try {
      final completionsJson = jsonDecode(completionsString) as List;
      return completionsJson
          .map((json) => ActivityCompletion.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error loading completions: $e');
      return [];
    }
  }

  // Limpiar todos los datos
  Future<void> clearAll() async {
    await init();
    await _prefs!.remove(_activitiesKey);
    await _prefs!.remove(_completionsKey);
  }

  // Guardar una actividad individual
  Future<void> saveActivity(Activity activity) async {
    final activities = await loadActivities();
    final index = activities.indexWhere((a) => a.id == activity.id);
    
    if (index >= 0) {
      activities[index] = activity;
    } else {
      activities.add(activity);
    }
    
    await saveActivities(activities);
  }

  // Eliminar una actividad
  Future<void> deleteActivity(String activityId) async {
    final activities = await loadActivities();
    activities.removeWhere((activity) => activity.id == activityId);
    await saveActivities(activities);

    // También eliminar todas las completions relacionadas
    final completions = await loadCompletions();
    completions.removeWhere((completion) => completion.activityId == activityId);
    await saveCompletions(completions);
  }

  // Guardar una completion individual
  Future<void> saveCompletion(ActivityCompletion completion) async {
    final completions = await loadCompletions();
    final index = completions.indexWhere((c) => 
        c.activityId == completion.activityId && 
        c.date == completion.date);
    
    if (index >= 0) {
      completions[index] = completion;
    } else {
      completions.add(completion);
    }
    
    await saveCompletions(completions);
  }

  // Obtener completions para una actividad específica
  Future<List<ActivityCompletion>> getCompletionsForActivity(String activityId) async {
    final completions = await loadCompletions();
    return completions.where((c) => c.activityId == activityId).toList();
  }

  // Obtener completions para una fecha específica
  Future<List<ActivityCompletion>> getCompletionsForDate(DateTime date) async {
    final completions = await loadCompletions();
    final targetDate = DateTime(date.year, date.month, date.day);
    return completions.where((c) => c.date == targetDate).toList();
  }

  // Verificar si una actividad está completada en una fecha específica
  Future<bool> isActivityCompletedOnDate(String activityId, DateTime date) async {
    final completions = await getCompletionsForDate(date);
    final completion = completions.firstWhere(
      (c) => c.activityId == activityId,
      orElse: () => ActivityCompletion.create(
        activityId: activityId,
        date: date,
        isCompleted: false,
      ),
    );
    return completion.isCompleted;
  }
}
