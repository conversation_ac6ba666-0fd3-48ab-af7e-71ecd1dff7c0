import 'package:flutter/material.dart';
import 'recurrence_pattern.dart';

class Activity {
  final String id;
  final String name;
  final String description;
  final DateTime startDate;
  final TimeOfDay? scheduledTime; // hora programada (opcional)
  final RecurrencePattern recurrencePattern;
  final String category; // ej: "<PERSON><PERSON><PERSON><PERSON>", "Trabajo", "Personal"
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Activity({
    required this.id,
    required this.name,
    required this.description,
    required this.startDate,
    this.scheduledTime,
    required this.recurrencePattern,
    this.category = 'General',
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  // Constructor para crear nueva actividad
  factory Activity.create({
    required String name,
    required String description,
    required DateTime startDate,
    TimeOfDay? scheduledTime,
    required RecurrencePattern recurrencePattern,
    String category = 'General',
  }) {
    final now = DateTime.now();
    return Activity(
      id: _generateId(),
      name: name,
      description: description,
      startDate: startDate,
      scheduledTime: scheduledTime,
      recurrencePattern: recurrencePattern,
      category: category,
      createdAt: now,
      updatedAt: now,
    );
  }

  // Generar ID único
  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  // Verificar si la actividad debe ocurrir en una fecha específica
  bool shouldOccurOn(DateTime date) {
    if (!isActive) return false;
    return recurrencePattern.shouldOccurOn(date, startDate);
  }

  // Crear copia con cambios
  Activity copyWith({
    String? name,
    String? description,
    DateTime? startDate,
    TimeOfDay? scheduledTime,
    RecurrencePattern? recurrencePattern,
    String? category,
    bool? isActive,
  }) {
    return Activity(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      startDate: startDate ?? this.startDate,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      recurrencePattern: recurrencePattern ?? this.recurrencePattern,
      category: category ?? this.category,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  // Convertir a Map para almacenamiento
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'startDate': startDate.millisecondsSinceEpoch,
      'scheduledTime': scheduledTime != null 
          ? {'hour': scheduledTime!.hour, 'minute': scheduledTime!.minute}
          : null,
      'recurrencePattern': recurrencePattern.toJson(),
      'category': category,
      'isActive': isActive,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  // Crear desde Map
  factory Activity.fromJson(Map<String, dynamic> json) {
    return Activity(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      startDate: DateTime.fromMillisecondsSinceEpoch(json['startDate']),
      scheduledTime: json['scheduledTime'] != null
          ? TimeOfDay(
              hour: json['scheduledTime']['hour'],
              minute: json['scheduledTime']['minute'],
            )
          : null,
      recurrencePattern: RecurrencePattern.fromJson(json['recurrencePattern']),
      category: json['category'] ?? 'General',
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(json['updatedAt']),
    );
  }

  @override
  String toString() {
    return 'Activity(id: $id, name: $name, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Activity && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
