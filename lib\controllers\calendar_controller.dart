import 'package:flutter/foundation.dart';
import 'package:table_calendar/table_calendar.dart';
import '../models/activity.dart';
import 'activity_controller.dart';

class CalendarController extends ChangeNotifier {
  final ActivityController _activityController;
  
  DateTime _selectedDay = DateTime.now();
  DateTime _focusedDay = DateTime.now();
  CalendarFormat _calendarFormat = CalendarFormat.month;

  CalendarController(this._activityController) {
    // Escuchar cambios en el ActivityController
    _activityController.addListener(_onActivitiesChanged);
  }

  // Getters
  DateTime get selectedDay => _selectedDay;
  DateTime get focusedDay => _focusedDay;
  CalendarFormat get calendarFormat => _calendarFormat;

  // Callback cuando cambian las actividades
  void _onActivitiesChanged() {
    notifyListeners();
  }

  // Cambiar día seleccionado
  void selectDay(DateTime selectedDay, DateTime focusedDay) {
    if (!isSameDay(_selectedDay, selectedDay)) {
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;
      notifyListeners();
    }
  }

  // Cambiar formato del calendario
  void changeCalendarFormat(CalendarFormat format) {
    if (_calendarFormat != format) {
      _calendarFormat = format;
      notifyListeners();
    }
  }

  // Cambiar día enfocado (para navegación)
  void changeFocusedDay(DateTime focusedDay) {
    _focusedDay = focusedDay;
    notifyListeners();
  }

  // Obtener actividades para una fecha específica
  List<Activity> getActivitiesForDay(DateTime day) {
    return _activityController.getActivitiesForDate(day);
  }

  // Obtener actividades para el día seleccionado
  List<Activity> get selectedDayActivities {
    return getActivitiesForDay(_selectedDay);
  }

  // Verificar si hay actividades en una fecha específica
  bool hasActivitiesOnDay(DateTime day) {
    return getActivitiesForDay(day).isNotEmpty;
  }

  // Obtener el número de actividades para una fecha
  int getActivityCountForDay(DateTime day) {
    return getActivitiesForDay(day).length;
  }

  // Obtener el número de actividades completadas para una fecha
  int getCompletedActivityCountForDay(DateTime day) {
    final activities = getActivitiesForDay(day);
    return activities.where((activity) => 
        _activityController.isActivityCompletedOnDate(activity.id, day)).length;
  }

  // Obtener el porcentaje de cumplimiento para una fecha
  double getCompletionRateForDay(DateTime day) {
    final activities = getActivitiesForDay(day);
    if (activities.isEmpty) return 0.0;
    
    final completedCount = getCompletedActivityCountForDay(day);
    return completedCount / activities.length;
  }

  // Verificar si todas las actividades están completadas en una fecha
  bool areAllActivitiesCompletedOnDay(DateTime day) {
    final activities = getActivitiesForDay(day);
    if (activities.isEmpty) return false;
    
    return activities.every((activity) => 
        _activityController.isActivityCompletedOnDate(activity.id, day));
  }

  // Obtener días con actividades en un rango de fechas
  List<DateTime> getDaysWithActivitiesInRange(DateTime start, DateTime end) {
    final daysWithActivities = <DateTime>[];
    
    for (DateTime day = start; 
         day.isBefore(end) || day.isAtSameMomentAs(end); 
         day = day.add(const Duration(days: 1))) {
      if (hasActivitiesOnDay(day)) {
        daysWithActivities.add(day);
      }
    }
    
    return daysWithActivities;
  }

  // Obtener estadísticas del mes actual
  Map<String, dynamic> getCurrentMonthStats() {
    final now = DateTime.now();
    final firstDayOfMonth = DateTime(now.year, now.month, 1);
    final lastDayOfMonth = DateTime(now.year, now.month + 1, 0);
    
    int totalDaysWithActivities = 0;
    int totalActivities = 0;
    int totalCompletedActivities = 0;
    
    for (DateTime day = firstDayOfMonth; 
         day.isBefore(lastDayOfMonth) || day.isAtSameMomentAs(lastDayOfMonth); 
         day = day.add(const Duration(days: 1))) {
      
      final dayActivities = getActivitiesForDay(day);
      if (dayActivities.isNotEmpty) {
        totalDaysWithActivities++;
        totalActivities += dayActivities.length;
        totalCompletedActivities += getCompletedActivityCountForDay(day);
      }
    }
    
    return {
      'totalDaysWithActivities': totalDaysWithActivities,
      'totalActivities': totalActivities,
      'totalCompletedActivities': totalCompletedActivities,
      'completionRate': totalActivities > 0 ? (totalCompletedActivities / totalActivities) : 0.0,
      'daysInMonth': lastDayOfMonth.day,
    };
  }

  // Navegar al día anterior
  void goToPreviousDay() {
    final previousDay = _selectedDay.subtract(const Duration(days: 1));
    selectDay(previousDay, previousDay);
  }

  // Navegar al día siguiente
  void goToNextDay() {
    final nextDay = _selectedDay.add(const Duration(days: 1));
    selectDay(nextDay, nextDay);
  }

  // Navegar al día actual
  void goToToday() {
    final today = DateTime.now();
    selectDay(today, today);
  }

  // Verificar si el día seleccionado es hoy
  bool get isSelectedDayToday {
    final today = DateTime.now();
    return isSameDay(_selectedDay, today);
  }

  // Verificar si el día seleccionado es en el futuro
  bool get isSelectedDayInFuture {
    final today = DateTime.now();
    return _selectedDay.isAfter(DateTime(today.year, today.month, today.day));
  }

  @override
  void dispose() {
    _activityController.removeListener(_onActivitiesChanged);
    super.dispose();
  }
}
