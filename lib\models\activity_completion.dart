class ActivityCompletion {
  final String id;
  final String activityId;
  final DateTime date; // fecha para la cual se completó la actividad
  final DateTime completedAt; // timestamp de cuándo se marcó como completada
  final bool isCompleted;
  final String? notes; // notas opcionales del usuario
  final Duration? actualDuration; // duración real de la actividad (opcional)

  const ActivityCompletion({
    required this.id,
    required this.activityId,
    required this.date,
    required this.completedAt,
    required this.isCompleted,
    this.notes,
    this.actualDuration,
  });

  // Constructor para crear nueva completion
  factory ActivityCompletion.create({
    required String activityId,
    required DateTime date,
    required bool isCompleted,
    String? notes,
    Duration? actualDuration,
  }) {
    return ActivityCompletion(
      id: _generateId(),
      activityId: activityId,
      date: DateTime(date.year, date.month, date.day), // solo fecha, sin hora
      completedAt: DateTime.now(),
      isCompleted: isCompleted,
      notes: notes,
      actualDuration: actualDuration,
    );
  }

  // Generar ID único
  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  // Crear copia con cambios
  ActivityCompletion copyWith({
    bool? isCompleted,
    String? notes,
    Duration? actualDuration,
  }) {
    return ActivityCompletion(
      id: id,
      activityId: activityId,
      date: date,
      completedAt: DateTime.now(),
      isCompleted: isCompleted ?? this.isCompleted,
      notes: notes ?? this.notes,
      actualDuration: actualDuration ?? this.actualDuration,
    );
  }

  // Convertir a Map para almacenamiento
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'activityId': activityId,
      'date': date.millisecondsSinceEpoch,
      'completedAt': completedAt.millisecondsSinceEpoch,
      'isCompleted': isCompleted,
      'notes': notes,
      'actualDuration': actualDuration?.inMinutes,
    };
  }

  // Crear desde Map
  factory ActivityCompletion.fromJson(Map<String, dynamic> json) {
    return ActivityCompletion(
      id: json['id'],
      activityId: json['activityId'],
      date: DateTime.fromMillisecondsSinceEpoch(json['date']),
      completedAt: DateTime.fromMillisecondsSinceEpoch(json['completedAt']),
      isCompleted: json['isCompleted'],
      notes: json['notes'],
      actualDuration: json['actualDuration'] != null
          ? Duration(minutes: json['actualDuration'])
          : null,
    );
  }

  // Obtener clave única para la combinación actividad-fecha
  String get uniqueKey => '${activityId}_${date.millisecondsSinceEpoch}';

  @override
  String toString() {
    return 'ActivityCompletion(id: $id, activityId: $activityId, date: $date, completed: $isCompleted)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ActivityCompletion && 
           other.activityId == activityId && 
           other.date == date;
  }

  @override
  int get hashCode => Object.hash(activityId, date);
}
