import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:provider/provider.dart';
import '../controllers/activity_controller.dart';
import '../controllers/calendar_controller.dart';
import '../models/activity.dart';
import 'activity_form_view.dart';

class CalendarView extends StatelessWidget {
  const CalendarView({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ActivityController()..init()),
        ChangeNotifierProxyProvider<ActivityController, CalendarController>(
          create: (context) => CalendarController(context.read<ActivityController>()),
          update: (context, activityController, previous) =>
              previous ?? CalendarController(activityController),
        ),
      ],
      child: const _CalendarViewContent(),
    );
  }
}

class _CalendarViewContent extends StatelessWidget {
  const _CalendarViewContent();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Calendario de Actividades'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.today),
            onPressed: () {
              context.read<CalendarController>().goToToday();
            },
            tooltip: 'Ir a hoy',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<ActivityController>().refresh();
            },
            tooltip: 'Actualizar',
          ),
        ],
      ),
      body: Consumer2<CalendarController, ActivityController>(
        builder: (context, calendarController, activityController, child) {
          if (activityController.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              // Calendario
              Card(
                margin: const EdgeInsets.all(8.0),
                child: TableCalendar<Activity>(
                  firstDay: DateTime.utc(2020, 1, 1),
                  lastDay: DateTime.utc(2030, 12, 31),
                  focusedDay: calendarController.focusedDay,
                  selectedDayPredicate: (day) {
                    return isSameDay(calendarController.selectedDay, day);
                  },
                  calendarFormat: calendarController.calendarFormat,
                  eventLoader: calendarController.getActivitiesForDay,
                  startingDayOfWeek: StartingDayOfWeek.monday,
                  calendarStyle: CalendarStyle(
                    outsideDaysVisible: false,
                    weekendTextStyle: TextStyle(color: Colors.red[400]),
                    holidayTextStyle: TextStyle(color: Colors.red[800]),
                  ),
                  headerStyle: const HeaderStyle(
                    formatButtonVisible: true,
                    titleCentered: true,
                    formatButtonShowsNext: false,
                    formatButtonDecoration: BoxDecoration(
                      color: Colors.deepOrange,
                      borderRadius: BorderRadius.all(Radius.circular(12.0)),
                    ),
                    formatButtonTextStyle: TextStyle(
                      color: Colors.white,
                    ),
                  ),
                  onDaySelected: calendarController.selectDay,
                  onFormatChanged: calendarController.changeCalendarFormat,
                  onPageChanged: calendarController.changeFocusedDay,
                  calendarBuilders: CalendarBuilders(
                    markerBuilder: (context, day, activities) {
                      if (activities.isEmpty) return null;
                      
                      final completedCount = calendarController.getCompletedActivityCountForDay(day);
                      final totalCount = activities.length;
                      final completionRate = completedCount / totalCount;
                      
                      Color markerColor;
                      if (completionRate == 1.0) {
                        markerColor = Colors.green;
                      } else if (completionRate > 0.5) {
                        markerColor = Colors.orange;
                      } else if (completionRate > 0) {
                        markerColor = Colors.red;
                      } else {
                        markerColor = Colors.grey;
                      }
                      
                      return Positioned(
                        right: 1,
                        bottom: 1,
                        child: Container(
                          decoration: BoxDecoration(
                            color: markerColor,
                            shape: BoxShape.circle,
                          ),
                          width: 16.0,
                          height: 16.0,
                          child: Center(
                            child: Text(
                              '$totalCount',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10.0,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              
              // Estadísticas del día seleccionado
              _buildDayStats(context, calendarController),
              
              // Lista de actividades del día seleccionado
              Expanded(
                child: _buildActivityList(context, calendarController, activityController),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const ActivityFormView(),
            ),
          );
        },
        tooltip: 'Agregar Actividad',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildDayStats(BuildContext context, CalendarController calendarController) {
    final selectedDay = calendarController.selectedDay;
    final activities = calendarController.selectedDayActivities;
    final completedCount = calendarController.getCompletedActivityCountForDay(selectedDay);
    final totalCount = activities.length;
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Column(
              children: [
                Text(
                  '$totalCount',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Text('Total'),
              ],
            ),
            Column(
              children: [
                Text(
                  '$completedCount',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.green,
                  ),
                ),
                const Text('Completadas'),
              ],
            ),
            Column(
              children: [
                Text(
                  '${totalCount > 0 ? ((completedCount / totalCount) * 100).round() : 0}%',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: totalCount > 0 && completedCount == totalCount 
                        ? Colors.green 
                        : Colors.orange,
                  ),
                ),
                const Text('Cumplimiento'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityList(
    BuildContext context, 
    CalendarController calendarController, 
    ActivityController activityController,
  ) {
    final activities = calendarController.selectedDayActivities;
    final selectedDay = calendarController.selectedDay;
    
    if (activities.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_available,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No hay actividades programadas para este día',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: activities.length,
      itemBuilder: (context, index) {
        final activity = activities[index];
        final isCompleted = activityController.isActivityCompletedOnDate(
          activity.id, 
          selectedDay,
        );
        
        return Card(
          child: ListTile(
            leading: Checkbox(
              value: isCompleted,
              onChanged: calendarController.isSelectedDayInFuture 
                  ? null 
                  : (value) {
                      activityController.toggleActivityCompletion(
                        activity.id, 
                        selectedDay,
                      );
                    },
            ),
            title: Text(
              activity.name,
              style: TextStyle(
                decoration: isCompleted ? TextDecoration.lineThrough : null,
                color: isCompleted ? Colors.grey : null,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (activity.description.isNotEmpty)
                  Text(activity.description),
                if (activity.scheduledTime != null)
                  Text(
                    'Hora: ${activity.scheduledTime!.format(context)}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                Text('Categoría: ${activity.category}'),
                Text('Patrón: ${activity.recurrencePattern}'),
              ],
            ),
            trailing: PopupMenuButton(
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Text('Editar'),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Text('Eliminar'),
                ),
              ],
              onSelected: (value) {
                if (value == 'edit') {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => ActivityFormView(activity: activity),
                    ),
                  );
                } else if (value == 'delete') {
                  _showDeleteConfirmation(context, activity, activityController);
                }
              },
            ),
            isThreeLine: true,
          ),
        );
      },
    );
  }

  void _showDeleteConfirmation(
    BuildContext context, 
    Activity activity, 
    ActivityController activityController,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmar eliminación'),
        content: Text('¿Estás seguro de que quieres eliminar "${activity.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () {
              activityController.deleteActivity(activity.id);
              Navigator.of(context).pop();
            },
            child: const Text('Eliminar'),
          ),
        ],
      ),
    );
  }
}
