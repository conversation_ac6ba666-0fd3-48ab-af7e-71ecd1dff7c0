enum RecurrenceType {
  daily,
  weekly,
  monthly,
  custom,
}

class RecurrencePattern {
  final RecurrenceType type;
  final int interval; // cada cuántos días/semanas/meses
  final List<int>? weekdays; // para recurrencia semanal (1=lunes, 7=domingo)
  final int? dayOfMonth; // para recurrencia mensual
  final DateTime? endDate; // fecha límite opcional

  const RecurrencePattern({
    required this.type,
    this.interval = 1,
    this.weekdays,
    this.dayOfMonth,
    this.endDate,
  });

  // Constructor para actividad diaria
  factory RecurrencePattern.daily({int interval = 1, DateTime? endDate}) {
    return RecurrencePattern(
      type: RecurrenceType.daily,
      interval: interval,
      endDate: endDate,
    );
  }

  // Constructor para actividad semanal
  factory RecurrencePattern.weekly({
    required List<int> weekdays,
    int interval = 1,
    DateTime? endDate,
  }) {
    return RecurrencePattern(
      type: RecurrenceType.weekly,
      interval: interval,
      weekdays: weekdays,
      endDate: endDate,
    );
  }

  // Constructor para actividad mensual
  factory RecurrencePattern.monthly({
    required int dayOfMonth,
    int interval = 1,
    DateTime? endDate,
  }) {
    return RecurrencePattern(
      type: RecurrenceType.monthly,
      interval: interval,
      dayOfMonth: dayOfMonth,
      endDate: endDate,
    );
  }

  // Verificar si la actividad debe ocurrir en una fecha específica
  bool shouldOccurOn(DateTime date, DateTime startDate) {
    if (endDate != null && date.isAfter(endDate!)) {
      return false;
    }

    if (date.isBefore(startDate)) {
      return false;
    }

    switch (type) {
      case RecurrenceType.daily:
        final daysDifference = date.difference(startDate).inDays;
        return daysDifference % interval == 0;

      case RecurrenceType.weekly:
        if (weekdays == null || weekdays!.isEmpty) return false;
        final weeksDifference = date.difference(startDate).inDays ~/ 7;
        if (weeksDifference % interval != 0) return false;
        return weekdays!.contains(date.weekday);

      case RecurrenceType.monthly:
        if (dayOfMonth == null) return false;
        final monthsDifference = (date.year - startDate.year) * 12 + 
                                (date.month - startDate.month);
        if (monthsDifference % interval != 0) return false;
        return date.day == dayOfMonth;

      case RecurrenceType.custom:
        return false; // Implementar lógica personalizada si es necesario
    }
  }

  // Convertir a Map para almacenamiento
  Map<String, dynamic> toJson() {
    return {
      'type': type.index,
      'interval': interval,
      'weekdays': weekdays,
      'dayOfMonth': dayOfMonth,
      'endDate': endDate?.millisecondsSinceEpoch,
    };
  }

  // Crear desde Map
  factory RecurrencePattern.fromJson(Map<String, dynamic> json) {
    return RecurrencePattern(
      type: RecurrenceType.values[json['type']],
      interval: json['interval'] ?? 1,
      weekdays: json['weekdays']?.cast<int>(),
      dayOfMonth: json['dayOfMonth'],
      endDate: json['endDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['endDate'])
          : null,
    );
  }

  @override
  String toString() {
    switch (type) {
      case RecurrenceType.daily:
        return interval == 1 ? 'Diario' : 'Cada $interval días';
      case RecurrenceType.weekly:
        final days = weekdays?.map((day) => _weekdayName(day)).join(', ') ?? '';
        return interval == 1 
            ? 'Semanal ($days)' 
            : 'Cada $interval semanas ($days)';
      case RecurrenceType.monthly:
        return interval == 1 
            ? 'Mensual (día $dayOfMonth)' 
            : 'Cada $interval meses (día $dayOfMonth)';
      case RecurrenceType.custom:
        return 'Personalizado';
    }
  }

  String _weekdayName(int weekday) {
    const names = ['Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb', 'Dom'];
    return names[weekday - 1];
  }
}
