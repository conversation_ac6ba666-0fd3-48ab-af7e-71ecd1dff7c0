import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/activity.dart';
import '../models/recurrence_pattern.dart';
import '../controllers/activity_controller.dart';

class ActivityFormView extends StatefulWidget {
  final Activity? activity; // null para crear nueva, no null para editar

  const ActivityFormView({super.key, this.activity});

  @override
  State<ActivityFormView> createState() => _ActivityFormViewState();
}

class _ActivityFormViewState extends State<ActivityFormView> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  DateTime _startDate = DateTime.now();
  TimeOfDay? _scheduledTime;
  String _category = 'General';
  RecurrenceType _recurrenceType = RecurrenceType.daily;
  int _interval = 1;
  List<int> _selectedWeekdays = [];
  int _dayOfMonth = 1;
  DateTime? _endDate;

  final List<String> _categories = [
    'General',
    'Ejercicio',
    'Trabajo',
    'Estudio',
    'Salud',
    'Personal',
    'Hogar',
    'Social',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.activity != null) {
      _loadActivityData();
    }
  }

  void _loadActivityData() {
    final activity = widget.activity!;
    _nameController.text = activity.name;
    _descriptionController.text = activity.description;
    _startDate = activity.startDate;
    _scheduledTime = activity.scheduledTime;
    _category = activity.category;
    _recurrenceType = activity.recurrencePattern.type;
    _interval = activity.recurrencePattern.interval;
    _selectedWeekdays = activity.recurrencePattern.weekdays ?? [];
    _dayOfMonth = activity.recurrencePattern.dayOfMonth ?? 1;
    _endDate = activity.recurrencePattern.endDate;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.activity == null ? 'Nueva Actividad' : 'Editar Actividad'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          TextButton(
            onPressed: _saveActivity,
            child: const Text('GUARDAR', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // Nombre
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Nombre de la actividad',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Por favor ingresa un nombre';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Descripción
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Descripción (opcional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),

            // Categoría
            DropdownButtonFormField<String>(
              value: _category,
              decoration: const InputDecoration(
                labelText: 'Categoría',
                border: OutlineInputBorder(),
              ),
              items: _categories.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(category),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _category = value!;
                });
              },
            ),
            const SizedBox(height: 16),

            // Fecha de inicio
            ListTile(
              title: const Text('Fecha de inicio'),
              subtitle: Text('${_startDate.day}/${_startDate.month}/${_startDate.year}'),
              trailing: const Icon(Icons.calendar_today),
              onTap: _selectStartDate,
            ),
            const Divider(),

            // Hora programada
            ListTile(
              title: const Text('Hora programada (opcional)'),
              subtitle: Text(_scheduledTime != null 
                  ? _scheduledTime!.format(context) 
                  : 'No especificada'),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (_scheduledTime != null)
                    IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        setState(() {
                          _scheduledTime = null;
                        });
                      },
                    ),
                  const Icon(Icons.access_time),
                ],
              ),
              onTap: _selectTime,
            ),
            const Divider(),

            // Tipo de recurrencia
            const Text(
              'Patrón de repetición',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),

            DropdownButtonFormField<RecurrenceType>(
              value: _recurrenceType,
              decoration: const InputDecoration(
                labelText: 'Tipo de repetición',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(
                  value: RecurrenceType.daily,
                  child: Text('Diario'),
                ),
                DropdownMenuItem(
                  value: RecurrenceType.weekly,
                  child: Text('Semanal'),
                ),
                DropdownMenuItem(
                  value: RecurrenceType.monthly,
                  child: Text('Mensual'),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _recurrenceType = value!;
                  if (_recurrenceType == RecurrenceType.weekly && _selectedWeekdays.isEmpty) {
                    _selectedWeekdays = [DateTime.now().weekday];
                  }
                });
              },
            ),
            const SizedBox(height: 16),

            // Intervalo
            TextFormField(
              initialValue: _interval.toString(),
              decoration: InputDecoration(
                labelText: _getIntervalLabel(),
                border: const OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Por favor ingresa un intervalo';
                }
                final interval = int.tryParse(value);
                if (interval == null || interval < 1) {
                  return 'El intervalo debe ser mayor a 0';
                }
                return null;
              },
              onChanged: (value) {
                final interval = int.tryParse(value);
                if (interval != null && interval > 0) {
                  _interval = interval;
                }
              },
            ),
            const SizedBox(height: 16),

            // Configuración específica según el tipo
            if (_recurrenceType == RecurrenceType.weekly) ...[
              const Text('Días de la semana:'),
              const SizedBox(height: 8),
              _buildWeekdaySelector(),
              const SizedBox(height: 16),
            ],

            if (_recurrenceType == RecurrenceType.monthly) ...[
              TextFormField(
                initialValue: _dayOfMonth.toString(),
                decoration: const InputDecoration(
                  labelText: 'Día del mes',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Por favor ingresa un día';
                  }
                  final day = int.tryParse(value);
                  if (day == null || day < 1 || day > 31) {
                    return 'El día debe estar entre 1 y 31';
                  }
                  return null;
                },
                onChanged: (value) {
                  final day = int.tryParse(value);
                  if (day != null && day >= 1 && day <= 31) {
                    _dayOfMonth = day;
                  }
                },
              ),
              const SizedBox(height: 16),
            ],

            // Fecha de fin (opcional)
            ListTile(
              title: const Text('Fecha de fin (opcional)'),
              subtitle: Text(_endDate != null 
                  ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}' 
                  : 'Sin fecha límite'),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (_endDate != null)
                    IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        setState(() {
                          _endDate = null;
                        });
                      },
                    ),
                  const Icon(Icons.event),
                ],
              ),
              onTap: _selectEndDate,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeekdaySelector() {
    const weekdays = [
      {'value': 1, 'label': 'Lun'},
      {'value': 2, 'label': 'Mar'},
      {'value': 3, 'label': 'Mié'},
      {'value': 4, 'label': 'Jue'},
      {'value': 5, 'label': 'Vie'},
      {'value': 6, 'label': 'Sáb'},
      {'value': 7, 'label': 'Dom'},
    ];

    return Wrap(
      spacing: 8.0,
      children: weekdays.map((weekday) {
        final isSelected = _selectedWeekdays.contains(weekday['value']);
        return FilterChip(
          label: Text(weekday['label'] as String),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              if (selected) {
                _selectedWeekdays.add(weekday['value'] as int);
              } else {
                _selectedWeekdays.remove(weekday['value']);
              }
              _selectedWeekdays.sort();
            });
          },
        );
      }).toList(),
    );
  }

  String _getIntervalLabel() {
    switch (_recurrenceType) {
      case RecurrenceType.daily:
        return 'Cada cuántos días';
      case RecurrenceType.weekly:
        return 'Cada cuántas semanas';
      case RecurrenceType.monthly:
        return 'Cada cuántos meses';
      case RecurrenceType.custom:
        return 'Intervalo';
    }
  }

  Future<void> _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );
    if (date != null) {
      setState(() {
        _startDate = date;
      });
    }
  }

  Future<void> _selectTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _scheduledTime ?? TimeOfDay.now(),
    );
    if (time != null) {
      setState(() {
        _scheduledTime = time;
      });
    }
  }

  Future<void> _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate ?? _startDate.add(const Duration(days: 30)),
      firstDate: _startDate,
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );
    if (date != null) {
      setState(() {
        _endDate = date;
      });
    }
  }

  void _saveActivity() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_recurrenceType == RecurrenceType.weekly && _selectedWeekdays.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Selecciona al menos un día de la semana')),
      );
      return;
    }

    try {
      final recurrencePattern = _createRecurrencePattern();
      
      if (widget.activity == null) {
        // Crear nueva actividad
        final activity = Activity.create(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          startDate: _startDate,
          scheduledTime: _scheduledTime,
          recurrencePattern: recurrencePattern,
          category: _category,
        );
        
        await context.read<ActivityController>().createActivity(activity);
      } else {
        // Actualizar actividad existente
        final updatedActivity = widget.activity!.copyWith(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          startDate: _startDate,
          scheduledTime: _scheduledTime,
          recurrencePattern: recurrencePattern,
          category: _category,
        );
        
        await context.read<ActivityController>().updateActivity(updatedActivity);
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.activity == null 
                ? 'Actividad creada exitosamente' 
                : 'Actividad actualizada exitosamente'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  RecurrencePattern _createRecurrencePattern() {
    switch (_recurrenceType) {
      case RecurrenceType.daily:
        return RecurrencePattern.daily(
          interval: _interval,
          endDate: _endDate,
        );
      case RecurrenceType.weekly:
        return RecurrencePattern.weekly(
          weekdays: _selectedWeekdays,
          interval: _interval,
          endDate: _endDate,
        );
      case RecurrenceType.monthly:
        return RecurrencePattern.monthly(
          dayOfMonth: _dayOfMonth,
          interval: _interval,
          endDate: _endDate,
        );
      case RecurrenceType.custom:
        return RecurrencePattern.daily(interval: _interval, endDate: _endDate);
    }
  }
}
