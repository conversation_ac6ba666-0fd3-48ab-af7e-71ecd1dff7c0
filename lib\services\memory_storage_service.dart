import '../models/activity.dart';
import '../models/activity_completion.dart';

class MemoryStorageService {
  // Singleton pattern
  static final MemoryStorageService _instance = MemoryStorageService._internal();
  factory MemoryStorageService() => _instance;
  MemoryStorageService._internal();

  final List<Activity> _activities = [];
  final List<ActivityCompletion> _completions = [];

  // Inicializar (no hace nada en memoria)
  Future<void> init() async {
    // No operation for memory storage
  }

  // Guardar actividades
  Future<void> saveActivities(List<Activity> activities) async {
    _activities.clear();
    _activities.addAll(activities);
  }

  // Cargar actividades
  Future<List<Activity>> loadActivities() async {
    return List.from(_activities);
  }

  // Guardar completions
  Future<void> saveCompletions(List<ActivityCompletion> completions) async {
    _completions.clear();
    _completions.addAll(completions);
  }

  // Cargar completions
  Future<List<ActivityCompletion>> loadCompletions() async {
    return List.from(_completions);
  }

  // Limpiar todos los datos
  Future<void> clearAll() async {
    _activities.clear();
    _completions.clear();
  }

  // Guardar una actividad individual
  Future<void> saveActivity(Activity activity) async {
    final index = _activities.indexWhere((a) => a.id == activity.id);
    
    if (index >= 0) {
      _activities[index] = activity;
    } else {
      _activities.add(activity);
    }
  }

  // Eliminar una actividad
  Future<void> deleteActivity(String activityId) async {
    _activities.removeWhere((activity) => activity.id == activityId);
    _completions.removeWhere((completion) => completion.activityId == activityId);
  }

  // Guardar una completion individual
  Future<void> saveCompletion(ActivityCompletion completion) async {
    final index = _completions.indexWhere((c) => 
        c.activityId == completion.activityId && 
        c.date == completion.date);
    
    if (index >= 0) {
      _completions[index] = completion;
    } else {
      _completions.add(completion);
    }
  }

  // Obtener completions para una actividad específica
  Future<List<ActivityCompletion>> getCompletionsForActivity(String activityId) async {
    return _completions.where((c) => c.activityId == activityId).toList();
  }

  // Obtener completions para una fecha específica
  Future<List<ActivityCompletion>> getCompletionsForDate(DateTime date) async {
    final targetDate = DateTime(date.year, date.month, date.day);
    return _completions.where((c) => c.date == targetDate).toList();
  }

  // Verificar si una actividad está completada en una fecha específica
  Future<bool> isActivityCompletedOnDate(String activityId, DateTime date) async {
    final completions = await getCompletionsForDate(date);
    final completion = completions.firstWhere(
      (c) => c.activityId == activityId,
      orElse: () => ActivityCompletion.create(
        activityId: activityId,
        date: date,
        isCompleted: false,
      ),
    );
    return completion.isCompleted;
  }
}
